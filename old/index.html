<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>J4</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            background-color: #0a192f;
            overflow: hidden;
            font-family: 'Consolas', monospace;
            color: #64ffda;
        }
        
        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10;
            pointer-events: none;
        }
        
        .j4-text {
            font-size: 120px;
            font-weight: bold;
            color: rgba(100, 255, 218, 0.2);
            text-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
            letter-spacing: 5px;
            mix-blend-mode: overlay;
        }
        
        .data-stream {
            position: absolute;
            bottom: 30px;
            width: 100%;
            text-align: center;
            font-size: 14px;
            overflow: hidden;
            white-space: nowrap;
        }
        
        .data-stream::before {
            /* content: "> Initializing J4 system... Establishing connection... "; */
            animation: streamData 10s linear infinite;
            display: inline-block;
        }
        
        .coordinates {
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 12px;
            opacity: 0.7;
        }
        
        .version {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 12px;
            opacity: 0.7;
        }
        
        @keyframes streamData {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    
    <div class="overlay">
        <div class="j4-text">J4</div>
    </div>
    
    <div class="coordinates">X: 142.53 | Y: 876.21</div>
    <div class="version">v1.0</div>
    <div class="data-stream"></div>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas dimensions
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            // Mouse position tracking
            let mouse = {
                x: undefined,
                y: undefined,
                radius: 150
            };
            
            // Track mouse movement
            canvas.addEventListener('mousemove', (event) => {
                mouse.x = event.x;
                mouse.y = event.y;
                
                // Update live coordinates
                document.querySelector('.coordinates').textContent = 
                    `X: ${mouse.x.toFixed(2)} | Y: ${mouse.y.toFixed(2)}`;
            });
            
            // Clear mouse position when mouse leaves
            canvas.addEventListener('mouseleave', () => {
                mouse.x = undefined;
                mouse.y = undefined;
            });
            
            // Particle settings
            const particleCount = 200;
            const particles = [];
            const connectionDistance = 150;
            
            // Create particles
            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    size: Math.random() * 2 + 1,
                    baseSize: Math.random() * 2 + 1,
                    speedX: (Math.random() - 0.5) * 1,
                    speedY: (Math.random() - 0.5) * 1,
                    baseSpeedX: (Math.random() - 0.5) * 1,
                    baseSpeedY: (Math.random() - 0.5) * 1,
                    color: `rgba(100, 255, 218, ${Math.random() * 0.5 + 0.2})`
                });
            }
            
            // Update coordinates display when not hovering
            setInterval(() => {
                if (mouse.x === undefined) {
                    document.querySelector('.coordinates').textContent = 
                        `X: ${(Math.random() * 1000).toFixed(2)} | Y: ${(Math.random() * 1000).toFixed(2)} | Z: ${(Math.random() * 100).toFixed(2)}`;
                }
            }, 2000);
            
            // Animation loop
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Draw grid background
                drawGrid();
                
                // Draw mouse effect when present
                if (mouse.x !== undefined) {
                    ctx.beginPath();
                    ctx.arc(mouse.x, mouse.y, 10, 0, Math.PI * 2);
                    ctx.fillStyle = 'rgba(100, 255, 218, 0.3)';
                    ctx.fill();
                    
                    ctx.beginPath();
                    ctx.arc(mouse.x, mouse.y, mouse.radius, 0, Math.PI * 2);
                    ctx.strokeStyle = 'rgba(100, 255, 218, 0.1)';
                    ctx.stroke();
                }
                
                // Update and draw particles
                for (let i = 0; i < particles.length; i++) {
                    const p = particles[i];
                    
                    // Interact with mouse
                    if (mouse.x !== undefined) {
                        const dx = p.x - mouse.x;
                        const dy = p.y - mouse.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance < mouse.radius) {
                            // Calculate force (inverse of distance)
                            const force = (mouse.radius - distance) / mouse.radius;
                            
                            // Apply force to particle (making it move away from cursor)
                            const angle = Math.atan2(dy, dx);
                            p.speedX = p.baseSpeedX + Math.cos(angle) * force * 2;
                            p.speedY = p.baseSpeedY + Math.sin(angle) * force * 2;
                            
                            // Increase size when near cursor
                            p.size = p.baseSize + (p.baseSize * force);
                        } else {
                            // Gradually return to base speed when not influenced
                            p.speedX = p.speedX * 0.98 + p.baseSpeedX * 0.02;
                            p.speedY = p.speedY * 0.98 + p.baseSpeedY * 0.02;
                            p.size = p.size * 0.9 + p.baseSize * 0.1;
                        }
                    } else {
                        // Return to base values when no mouse interaction
                        p.speedX = p.baseSpeedX;
                        p.speedY = p.baseSpeedY;
                        p.size = p.baseSize;
                    }
                    
                    // Move particles
                    p.x += p.speedX;
                    p.y += p.speedY;
                    
                    // Wrap around edges
                    if (p.x < 0) p.x = canvas.width;
                    if (p.x > canvas.width) p.x = 0;
                    if (p.y < 0) p.y = canvas.height;
                    if (p.y > canvas.height) p.y = 0;
                    
                    // Draw particle
                    ctx.fillStyle = p.color;
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Draw connections
                    for (let j = i + 1; j < particles.length; j++) {
                        const p2 = particles[j];
                        const distance = Math.sqrt(
                            Math.pow(p.x - p2.x, 2) + 
                            Math.pow(p.y - p2.y, 2)
                        );
                        
                        if (distance < connectionDistance) {
                            ctx.strokeStyle = `rgba(100, 255, 218, ${0.1 * (1 - distance / connectionDistance)})`;
                            ctx.lineWidth = 0.5;
                            ctx.beginPath();
                            ctx.moveTo(p.x, p.y);
                            ctx.lineTo(p2.x, p2.y);
                            ctx.stroke();
                        }
                    }
                }
                
                requestAnimationFrame(animate);
            }
            
            function drawGrid() {
                const gridSize = 30;
                ctx.strokeStyle = 'rgba(100, 255, 218, 0.1)';
                ctx.lineWidth = 0.5;
                
                // Vertical lines
                for (let x = 0; x < canvas.width; x += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, canvas.height);
                    ctx.stroke();
                }
                
                // Horizontal lines
                for (let y = 0; y < canvas.height; y += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                }
            }
            
            // Handle window resize
            window.addEventListener('resize', () => {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            });
            
            // Start animation
            animate();
        });
    </script>
</body>
</html>
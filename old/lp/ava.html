<!doctype html>
<html class="no-js" lang="en">
    <head id="test534344">
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link
            rel="icon"
            href="https://www.avatrade.com/wp-content/themes/ava_trade/favicon.ico"
        />
        <title>AvaTrade | Claim Your 100% Trading Bonus</title>
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <style id="critical_bundle">
            /* Basic Reset & Font */
            html {
                line-height: 1.15;
                -ms-text-size-adjust: 100%;
                -webkit-text-size-adjust: 100%;
            }
            body,
            html {
                font-family: SuisseIntl, Arial, sans-serif, Helvetica, Roboto;
            }
            body {
                margin: 0;
                padding: 0;
                background: #fefefe;
                font-weight: 400;
                line-height: 1.5;
                color: #343434;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            h1,
            h2,
            p,
            ul,
            li {
                /* Removed h3, h4, h5, h6 */
                margin: 0;
                padding: 0;
            }
            img {
                max-width: 100%;
                height: auto;
                border-style: none;
            }
            /* Removed button, input[type="submit"] rule as they are not used */
            .ava-container {
                width: 100%;
                padding-right: 15px;
                padding-left: 15px;
                margin-right: auto;
                margin-left: auto;
                box-sizing: border-box;
            }
            @media (min-width: 576px) {
                .ava-container {
                    max-width: 540px;
                }
            }
            @media (min-width: 768px) {
                .ava-container {
                    max-width: 720px;
                }
            }
            @media (min-width: 992px) {
                .ava-container {
                    max-width: 960px;
                }
            }
            @media (min-width: 1200px) {
                .ava-container {
                    max-width: 1140px;
                }
            }
            @media (min-width: 1400px) {
                .ava-container {
                    max-width: 1320px;
                }
            }

            .btn {
                display: inline-block;
                font-weight: 400;
                color: #212529;
                text-align: center;
                vertical-align: middle;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                background-color: transparent;
                border: 1px solid transparent;
                padding: 0.375rem 0.75rem;
                font-size: 1rem;
                line-height: 1.5;
                border-radius: 0.25rem;
                transition:
                    color 0.15s ease-in-out,
                    background-color 0.15s ease-in-out,
                    border-color 0.15s ease-in-out,
                    box-shadow 0.15s ease-in-out;
            }
            .btn-orange {
                color: #fff;
                background-color: #ff5100;
                border-color: #ff5100;
                min-width: 188px;
                max-width: fit-content;
                font-size: 16px;
                font-weight: 400;
                min-height: 44px;
                padding: 11px 30px;
                border-radius: 4px;
                text-decoration: none;
            }
            .btn-orange:hover {
                background-color: #e04000;
                border-color: #d33c00;
            }
            .btn-demo {
                color: #7e868e;
                text-decoration: none;
                font-size: 15px;
                font-weight: 400;
                padding: 12px;
                display: inline-block;
                margin-top: 10px;
            }
            .btn-demo:hover {
                color: #4c61ff;
                text-decoration: underline;
            }
        </style>
        <link
            rel="stylesheet"
            id="main_bundle_css-css"
            href="https://www.avatrade.com/wp-content/themes/ava_trade/dist/main.bundle.css?ver=20250515.2"
            type="text/css"
            media=""
        />
        <style id="shared_css">
            /* Using existing styles for hero, buttons, etc. */
            .hp-top-section {
                position: relative;
                padding-top: 20px;
                padding-bottom: 40px;
                text-align: center;
            }
            .home-wrapper-brand .hp-top-section {
                background: linear-gradient(
                    180deg,
                    #e2e3ff78 0%,
                    rgba(255, 255, 255, 0) 28.97%
                );
                min-height: auto;
            }
            .home-wrapper-brand .hp-top-section h1 {
                font-size: 30px;
                text-align: center;
                font-weight: 600;
                color: #101820;
                line-height: 1.2;
                margin-top: 5px;
                margin-bottom: 15px;
            }
            .home-wrapper-brand .hp-top-section h1 span {
                color: #4c61ff;
            }
            .home-wrapper-brand .hp-top-section p {
                color: #101820;
                font-size: 16px;
                text-align: center;
                font-weight: 400;
                max-width: 550px;
                margin: 0 auto 20px auto;
                line-height: 1.6;
            }
            .home-wrapper-brand .hp-top-section .btn-box {
                margin-bottom: 20px;
                margin-top: 15px;
            }
            .home-wrapper-brand .hp-top-section .btn-box .btn:first-child {
                margin: 0 auto 10px auto;
                display: block;
            }
            .home-wrapper-brand .hp-top-section .btn-box .btn-link {
                margin: 0 auto;
                display: block;
            }
            /* Removed .home-wrapper-brand .hp-top-section .trustpilot-logo rule */
            .home-wrapper-brand .hp-top-section .award-img img {
                max-height: 125px;
                margin: 10px auto 0 auto;
                display: block;
            }
            .promotion-t_c {
                font-size: 12px;
                color: #555;
                text-align: center;
                margin-top: 15px;
            }
            .promotion-t_c a {
                color: #555;
                text-decoration: underline;
            }

            .why-avatrade-section {
                /* Removed .awards-lp-section, .final-cta-section */
                padding: 40px 0;
                text-align: center;
            }
            .why-avatrade-section h2 {
                /* Removed .awards-lp-section h2, .final-cta-section h2 */
                font-size: 28px;
                color: #101820;
                margin-bottom: 25px;
                font-weight: 600;
            }
            .why-avatrade-section ul {
                list-style: none;
                padding: 0;
                max-width: 600px;
                margin: 0 auto 20px auto;
            }
            .why-avatrade-section ul li {
                font-size: 16px;
                line-height: 1.8;
                margin-bottom: 15px;
                padding-left: 25px;
                position: relative;
                text-align: left;
            }
            .why-avatrade-section ul li::before {
                content: "✓";
                color: #4caf50;
                position: absolute;
                left: 0;
                font-weight: bold;
            }
            /* Removed .awards-lp-section .awards-grid rule */
            /* Removed .awards-lp-section .awards-grid img rule */
            .lp-footer {
                background-color: #f8f9fa;
                padding: 20px 0;
                text-align: center;
                font-size: 14px;
                color: #555;
            }
            .lp-footer p {
                margin-bottom: 10px;
            }
            .lp-footer a {
                color: #007bff;
                text-decoration: none;
                margin: 0 10px;
            }
            .lp-footer a:hover {
                text-decoration: underline;
            }
            .lp-header {
                padding: 15px 0;
                border-bottom: 1px solid #eee;
            }
            .lp-header .ava-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .lp-header .logo img {
                max-height: 30px;
            }
            /* Removed .lp-header .btn-header .btn-orange rule */

            @media (min-width: 768px) {
                .home-wrapper-brand .hp-top-section h1 {
                    font-size: 44px;
                }
                .home-wrapper-brand .hp-top-section p {
                    font-size: 18px;
                }
                .home-wrapper-brand .hp-top-section .btn-box {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 20px;
                }
                .home-wrapper-brand .hp-top-section .btn-box .btn:first-child {
                    margin: 0;
                    display: inline-block;
                }
                .home-wrapper-brand .hp-top-section .btn-box .btn-link {
                    margin: 0;
                    display: inline-block;
                }
                .home-wrapper-brand .hp-top-section .t-pilot-awards-wrap {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 30px;
                    margin-top: 25px;
                }
                /* Removed .home-wrapper-brand .hp-top-section .trustpilot-logo rule from media query */
                .home-wrapper-brand .hp-top-section .award-img img {
                    margin: 0;
                }
                .why-avatrade-section h2 {
                    /* Removed .awards-lp-section h2, .final-cta-section h2 from media query */
                    font-size: 32px;
                }
                .lp-header .logo img {
                    max-height: 35px;
                }
            }
            /* Ensure promotion content is visible */
            .hp-top-section .content.promotion {
                display: block !important;
            }
            .hp-top-section .content.none-promotion {
                display: none !important;
            }
        </style>
    </head>
    <body
        class="home page-template-template-front-page lang-en landing-page-body"
    >
        <header class="lp-header">
            <div class="ava-container">
                <div class="logo">
                    <a href="/">
                        <img
                            src="./ava_logo-brand-dark.svg"
                            alt="AvaTrade Logo"
                        />
                    </a>
                </div>
            </div>
        </header>
<script>
const urlParams = new URLSearchParams(window.location.search);
const websiteId = urlParams.get('WEBSITEID') || 'none';
const impressionId = urlParams.get('IMPRESSIONID') || 'none';
</script>
        <main id="main">
            <div class="home-wrapper-brand">
                <section class="section-wrap hp-top-section">
                    <div class="ava-container">
                        <div class="content promotion">
                            <h1>
                                Double Up! <br />with a <span>100% Bonus</span>
                            </h1>
                            <p>
                                Start trading CFDs with a 100% bonus on your
                                first deposit. Trade FX, Commodities like Gold &
                                Oil, Indices, Cryptocurrencies, and more with
                                double the power!
                            </p>
                            <div class="btn-box center">
                                <a
                                    class="btn btn-orange"
                                    href="#"
                                    onclick="window.location.href='https://tracking.avapartner.com/click/?affid=88972&campaign=216845&campaignName=PA&source=' + encodeURIComponent(websiteId) + '&clickid=' + encodeURIComponent(impressionId) + '&TargetUrl=https%3A%2F%2Fwww.avatrade.com%2F%3Fact%3Dshow-real-registry%26versionId%3D10301%26tag%3D88972';"
                                    rel="nofollow"
                                    >Claim Your 100% Bonus Now</a
                                >
                                <a
                                    class="btn btn-demo btn-link"
                                    href="#"
                                    onclick="window.location.href='https://tracking.avapartner.com/click/?affid=88972&campaign=216845&campaignName=PA&source=' + encodeURIComponent(websiteId) + '&clickid=' + encodeURIComponent(impressionId) + '&TargetUrl=https%3A%2F%2Fwww.avatrade.com%2F%3Fact%3Dshow-real-registry%26versionId%3D10301%26tag%3D88972';"
                                    rel="nofollow"
                                    >Try Free Demo</a
                                >
                            </div>
                            <div class="t-pilot-awards-wrap">
                                <div class="award-img">
                                    <img
                                        src="./ava_award-2025-pro.png"
                                        alt="Award Winning Broker 2025"
                                    />
                                </div>
                            </div>
                            <div class="promotion-t_c">
                                *Eligible for first-time deposits of $100 to
                                $300. T&Cs apply.
                            </div>
                        </div>
                        </div>
                    </div>
                </section>

                <section class="why-avatrade-section">
                    <div class="ava-container">
                        <h2>Why Trade With AvaTrade?</h2>
                        <ul>
                            <li>
                                <strong>Globally Regulated & Trusted:</strong>
                                Secure trading with a broker regulated across 9
                                jurisdictions worldwide.
                            </li>
                            <li>
                                <strong>Wide Range of Instruments:</strong>
                                Trade CFDs on Forex, Stocks, Commodities,
                                Cryptocurrencies, Indices & Options.
                            </li>
                            <li>
                                <strong>Powerful Platforms:</strong> Access
                                advanced trading technology on desktop and
                                mobile, including MetaTrader 4/5 and AvaTradeGO.
                            </li>
                            <li>
                                <strong>Competitive Trading Conditions:</strong>
                                Benefit from tight spreads, fast execution, and
                                leverage up to
                                <i class="dc bg-leverage-major">30:1</i>
                                (leverage depends on asset and regulation).
                            </li>
                            <li>
                                <strong>Dedicated Support:</strong> Get
                                multilingual customer support whenever the
                                markets are open.
                            </li>
                        </ul>
                    </div>
                </section>
            </div>
        </main>

        <footer class="lp-footer">
            <div class="ava-container">
                <p class="copyright">
                    Copyright © 2007-<span class="copyright-year">2025</span>
                    Ava Trade Markets Ltd. All rights reserved.
                </p>
            </div>
        </footer>
    </body>
</html>

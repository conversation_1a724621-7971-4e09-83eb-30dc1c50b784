/* Variables for better consistency */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --background-color: #f5f5f5;
    --border-color: #ddd;
    --button-hover-color: #2980b9;
    --transition-speed: 0.3s;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: var(--background-color);
    line-height: 1.6;
}

/* Main container */
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    position: relative;
}

/* Textarea container */
.textarea-container {
    height: 0;
    overflow: hidden;
    transition: height var(--transition-speed) ease;
    background-color: white;
    border-bottom: 1px solid var(--border-color);
}

/* Editor wrapper for code highlighting */
.editor-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* HTML input textarea */
.html-input {
    width: 100%;
    height: 100%;
    padding: 15px;
    border: none;
    resize: none;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    outline: none;
    position: absolute;
    top: 0;
    left: 0;
    color: transparent;
    background: transparent;
    caret-color: black; /* Make cursor visible */
    z-index: 2;
    white-space: pre;
    overflow: auto;
    line-height: 1.6;
    tab-size: 4;
}

/* Highlighted code container */
#highlightedCode {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 15px;
    border: none;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: transparent;
    pointer-events: none;
    overflow: auto;
    z-index: 1;
    line-height: 1.6;
    tab-size: 4;
}

/* Override some Prism styles to match our layout */
code[class*="language-"], 
pre[class*="language-"] {
    text-shadow: none;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.6;
    tab-size: 4;
    hyphens: none;
}

/* Button container */
.button-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 10;
    transition: transform var(--transition-speed) ease;
}

/* Paste button */
.paste-button {
    background-color: var(--primary-color);
    color: white;
    padding: 8px 20px;
    border: none;
    border-radius: 4px 4px 0 0;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.paste-button:hover {
    background-color: var(--button-hover-color);
}

/* Drag handle */
.drag-handle {
    width: 40px;
    height: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: grab;
    margin-top: 5px;
}

.dot-row {
    display: flex;
    justify-content: center;
    margin: 2px 0;
}

.dot {
    width: 4px;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    margin: 0 2px;
}

/* Preview frame */
.preview-frame {
    flex-grow: 1;
    width: 100%;
    height: 100%;
    border: none;
    background-color: white;
    transition: height 0.4s ease-in-out;
}

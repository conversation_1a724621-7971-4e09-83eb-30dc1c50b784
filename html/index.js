// Constants for better readability
const DEFAULT_TEXTAREA_HEIGHT = 250; // Height in pixels when textarea is open
const MIN_DRAG_THRESHOLD = 5; // Minimum drag in pixels to trigger animation
let isTextareaOpen = false; // Track textarea state
let hasMovedEnough = false; // Track if drag has moved enough to trigger animation

// Get DOM elements
const dragHandle = document.getElementById('dragHandle');
const textareaContainer = document.getElementById('textareaContainer');
const htmlInput = document.getElementById('htmlInput');
const previewFrame = document.getElementById('previewFrame');
const buttonContainer = document.getElementById('buttonContainer');
const highlightedCode = document.querySelector('#highlightedCode code');

// Variables to track drag state
let isDragging = false;
let startY = 0;
let currentHeight = 0;

/**
 * Updates the preview iframe with the content from the textarea
 */
function updatePreview() {
    console.log('Updating preview with content length:', htmlInput.value.length);
    const htmlContent = htmlInput.value;
    previewFrame.srcdoc = htmlContent;
}

/**
 * Updates the syntax highlighting in the code element
 */
function updateHighlighting() {
    // Escape HTML entities to prevent XSS
    const escapedCode = htmlInput.value
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
    
    // Update the code content
    highlightedCode.innerHTML = escapedCode;
    
    // Always add padding element for better scrolling
    if (!document.getElementById('scroll-padding')) {
        const paddingElement = document.createElement('div');
        paddingElement.id = 'scroll-padding';
        paddingElement.style.height = '150px';
        paddingElement.style.width = '1px';
        paddingElement.style.display = 'block';
        highlightedCode.appendChild(paddingElement);
    }
    
    // Apply Prism highlighting
    Prism.highlightElement(highlightedCode);
    
    // Ensure scroll sync
    syncScroll();
}

/**
 * Synchronize scroll positions between textarea and highlighted code
 */
function syncScroll() {
    const highlightedPreElement = document.getElementById('highlightedCode');
    highlightedPreElement.scrollTop = htmlInput.scrollTop;
    highlightedPreElement.scrollLeft = htmlInput.scrollLeft;
    
    // Ensure we can see the cursor at the bottom by adding a small padding element if needed
    const textLength = htmlInput.value.length;
    const cursorPosition = htmlInput.selectionStart;
    
    // If cursor is near the end of text, ensure it's visible
    if (cursorPosition > textLength - 20) {
        // Add padding to the highlighted code if needed
        if (!document.getElementById('scroll-padding')) {
            const paddingElement = document.createElement('div');
            paddingElement.id = 'scroll-padding';
            paddingElement.style.height = '150px'; // Extra space at the bottom
            paddingElement.style.width = '1px';
            paddingElement.style.display = 'block';
            highlightedCode.appendChild(paddingElement);
        }
    }
}

/**
 * Handles the start of dragging
 * @param {MouseEvent|TouchEvent} e - The event object
 */
function handleDragStart(e) {
    console.log('Drag start detected:', e.type);
    isDragging = true;
    hasMovedEnough = false; // Reset the movement tracker
    
    // Get the appropriate Y coordinate based on event type
    startY = e.type === 'mousedown' ? e.clientY : e.touches[0].clientY;
    console.log('Start Y position:', startY);
    
    // Get current height of textarea container
    currentHeight = parseInt(window.getComputedStyle(textareaContainer).height);
    console.log('Current textarea height:', currentHeight);
    
    // Add event listeners for drag movement and end
    if (e.type === 'mousedown') {
        document.addEventListener('mousemove', handleDragMove);
        document.addEventListener('mouseup', handleDragEnd);
        console.log('Added mouse event listeners');
    } else {
        document.addEventListener('touchmove', handleDragMove, { passive: false });
        document.addEventListener('touchend', handleDragEnd);
        console.log('Added touch event listeners');
    }
    
    // Prevent default behavior to avoid text selection during drag
    e.preventDefault();
    e.stopPropagation(); // Stop event from bubbling to parent elements
}

/**
 * Handles the drag movement
 * @param {MouseEvent|TouchEvent} e - The event object
 */
function handleDragMove(e) {
    if (!isDragging) return;
    
    // Get the appropriate Y coordinate based on event type
    const currentY = e.type === 'mousemove' ? e.clientY : e.touches[0].clientY;
    const deltaY = currentY - startY;
    console.log('Current Y position:', currentY, 'Delta:', deltaY);
    
    // Check if we've moved enough to consider this a significant drag
    if (Math.abs(deltaY) >= MIN_DRAG_THRESHOLD) {
        hasMovedEnough = true;
        console.log('Drag has moved enough to trigger animation');
        
        // Immediately trigger the animation when threshold is reached
        if (deltaY > 0 && !isTextareaOpen) {
            // Dragged down enough to open
            console.log('Drag down detected, immediately triggering open animation');
            openTextarea();
            
            // Remove event listeners early since we've already triggered the animation
            document.removeEventListener('mousemove', handleDragMove);
            document.removeEventListener('mouseup', handleDragEnd);
            document.removeEventListener('touchmove', handleDragMove);
            document.removeEventListener('touchend', handleDragEnd);
            isDragging = false;
        } else if (deltaY < 0 && isTextareaOpen) {
            // Dragged up enough to close
            console.log('Drag up detected, immediately triggering close animation');
            closeTextarea();
            
            // Remove event listeners early since we've already triggered the animation
            document.removeEventListener('mousemove', handleDragMove);
            document.removeEventListener('mouseup', handleDragEnd);
            document.removeEventListener('touchmove', handleDragMove);
            document.removeEventListener('touchend', handleDragEnd);
            isDragging = false;
        }
    }
    
    // Prevent default to avoid scrolling on touch devices
    e.preventDefault();
}

/**
 * Handles the end of dragging
 * @param {MouseEvent|TouchEvent} e - The event object
 */
function handleDragEnd(e) {
    console.log('Drag end detected');
    
    // Get the final Y position
    const finalY = e.type === 'mouseup' ? e.clientY : e.changedTouches[0].clientY;
    const deltaY = finalY - startY;
    console.log('Final drag delta:', deltaY, 'Has moved enough:', hasMovedEnough);
    
    // Remove event listeners
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
    document.removeEventListener('touchmove', handleDragMove);
    document.removeEventListener('touchend', handleDragEnd);
    console.log('Removed event listeners');
    
    // Only trigger animation if we've moved enough during the drag
    // This prevents triggering on simple clicks
    if (hasMovedEnough) {
        // Trigger animation based on drag direction
        if (deltaY > MIN_DRAG_THRESHOLD && !isTextareaOpen) {
            // Dragged down enough to open
            console.log('Drag down detected, triggering open animation');
            openTextarea();
        } else if (deltaY < -MIN_DRAG_THRESHOLD && isTextareaOpen) {
            // Dragged up enough to close
            console.log('Drag up detected, triggering close animation');
            closeTextarea();
        }
    } else {
        console.log('Not enough movement to trigger animation');
    }
    
    isDragging = false;
    hasMovedEnough = false;
}

/**
 * Open the textarea with animation
 */
function openTextarea() {
    console.log('Opening textarea with animation');
    textareaContainer.style.height = DEFAULT_TEXTAREA_HEIGHT + 'px';
    buttonContainer.style.transform = `translateY(${DEFAULT_TEXTAREA_HEIGHT - 43}px)`;
    isTextareaOpen = true;
}

/**
 * Close the textarea with animation
 */
function closeTextarea() {
    console.log('Closing textarea with animation');
    textareaContainer.style.height = '0';
    buttonContainer.style.transform = 'translateY(0)';
    isTextareaOpen = false;
}

/**
 * Toggle the textarea visibility when clicking the paste button
 */
function toggleTextarea() {
    console.log('Toggle textarea - current state:', isTextareaOpen);
    
    if (!isTextareaOpen) {
        openTextarea();
    } else {
        closeTextarea();
    }
}

/**
 * Read from clipboard and paste into textarea
 */
async function pasteFromClipboard() {
    console.log('Attempting to read from clipboard...');
    try {
        // Check if clipboard API is available
        if (navigator.clipboard) {
            console.log('Clipboard API object exists:', !!navigator.clipboard);
            console.log('readText method exists:', !!navigator.clipboard.readText);
            
            if (navigator.clipboard.readText) {
                console.log('Calling clipboard.readText()...');
                const text = await navigator.clipboard.readText();
                console.log('Successfully read from clipboard, content length:', text.length);
                htmlInput.value = text;
                updateHighlighting(); // Update syntax highlighting
                updatePreview();
            } else {
                console.error('Clipboard readText method not available');
                alert('Clipboard reading is not available in your browser. Please paste manually with Ctrl+V/Cmd+V.');
            }
        } else {
            console.error('Clipboard API not available');
            alert('Clipboard access is not available in your browser. Please paste manually with Ctrl+V/Cmd+V.');
        }
    } catch (err) {
        console.error('Failed to read clipboard contents: ', err);
        alert('Unable to access clipboard: ' + err.message + '. Please paste manually with Ctrl+V/Cmd+V or check browser permissions.');
    }
}

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for drag handle
    console.log('Setting up drag handle event listeners');
    dragHandle.addEventListener('mousedown', handleDragStart);
    dragHandle.addEventListener('touchstart', handleDragStart);
    console.log('Drag handle element:', dragHandle);
    
    // Prevent any action when clicking directly on the drag handle
    dragHandle.addEventListener('click', function(e) {
        console.log('Drag handle clicked directly - preventing any action');
        e.preventDefault();
        e.stopPropagation(); // Prevent the click from bubbling to the button
    });
    
    // Also prevent clicks on dot elements from triggering actions
    document.querySelectorAll('.dot, .dot-row').forEach(element => {
        element.addEventListener('click', function(e) {
            console.log('Dot element clicked - preventing any action');
            e.preventDefault();
            e.stopPropagation();
        });
    });
    
    // Initialize with textarea closed
    textareaContainer.style.height = '0';
    
    // Update preview and highlighting on input changes
    htmlInput.addEventListener('input', function() {
        updateHighlighting();
        updatePreview();
    });
    
    // Sync scroll positions
    htmlInput.addEventListener('scroll', syncScroll);
    
    // Handle tab key in the textarea
    htmlInput.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            e.preventDefault();
            const start = this.selectionStart;
            const end = this.selectionEnd;
            
            // Insert 4 spaces at the cursor position
            this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
            
            // Move the cursor after the inserted spaces
            this.selectionStart = this.selectionEnd = start + 4;
            
            // Update the highlighting
            updateHighlighting();
        }
    });
    
    // Also handle paste events specifically
    htmlInput.addEventListener('paste', function() {
        // Use setTimeout to ensure we get the pasted content
        setTimeout(function() {
            updateHighlighting();
            updatePreview();
        }, 0);
    });
    
    // Handle paste button clicks for clipboard operations only
    document.querySelector('.paste-button').addEventListener('click', function(e) {
        console.log('Paste button clicked, target:', e.target.tagName, e.target.className);
        
        // Check if the click was on the drag handle or its children (dots)
        const isDragHandleOrChild = e.target === dragHandle || 
                                   dragHandle.contains(e.target) || 
                                   e.target.classList.contains('dot') || 
                                   e.target.classList.contains('dot-row');
        
        // Only process paste operations if not clicking on drag handle elements
        if (!isDragHandleOrChild) {
            console.log('Click was on button text, handling paste operation');
            
            // Just handle the paste operation without toggling the textarea
            pasteFromClipboard();
        } else {
            console.log('Click was on drag handle or dots, ignoring click');
            // Prevent any action when clicking on the drag handle
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    // Initial highlighting (if there's any content)
    updateHighlighting();
});

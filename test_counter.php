<?php
/**
 * Script de test pour le compteur de visiteurs
 */

echo "Test du compteur de visiteurs SQLite\n";
echo "=====================================\n\n";

// Test 1: Récupérer le compteur initial
echo "1. Test de récupération du compteur:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/visitor_counter.php?action=get');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['HTTP_REFERER: http://localhost/']);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Code HTTP: $httpCode\n";
echo "Réponse: $response\n\n";

// Test 2: Incrémenter le compteur
echo "2. Test d'incrémentation du compteur:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/visitor_counter.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, 'action=count');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['HTTP_REFERER: http://localhost/']);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Code HTTP: $httpCode\n";
echo "Réponse: $response\n\n";

// Test 3: Vérifier le nouveau compteur
echo "3. Vérification du nouveau compteur:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/visitor_counter.php?action=get');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['HTTP_REFERER: http://localhost/']);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Code HTTP: $httpCode\n";
echo "Réponse: $response\n\n";

// Test 4: Vérifier la base de données directement
echo "4. Vérification directe de la base de données:\n";
if (file_exists('visitors.db')) {
    try {
        $pdo = new PDO('sqlite:visitors.db');
        $stmt = $pdo->query("SELECT * FROM visitor_count");
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Contenu de la table visitor_count:\n";
        print_r($results);
    } catch (PDOException $e) {
        echo "Erreur PDO: " . $e->getMessage() . "\n";
    }
} else {
    echo "Le fichier visitors.db n'existe pas encore.\n";
}

echo "\nTest terminé.\n";
?>

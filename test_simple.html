<!DOCTYPE html>
<html>
<head>
    <title>Test Compteur Simple</title>
</head>
<body>
    <h1>Test du Compteur</h1>
    <p>Compteur actuel: <span id="count">---</span></p>
    <button onclick="testGet()">Test GET</button>
    <button onclick="testPost()">Test POST</button>
    <button onclick="testSendBeacon()">Test SendBeacon</button>
    
    <div id="results"></div>

    <script>
        function log(message) {
            console.log(message);
            document.getElementById('results').innerHTML += '<p>' + message + '</p>';
        }

        function testGet() {
            log('Test GET...');
            fetch('visitor_counter.php?action=get')
                .then(response => response.json())
                .then(data => {
                    log('GET résultat: ' + JSON.stringify(data));
                    if (data.success) {
                        document.getElementById('count').textContent = data.count;
                    }
                })
                .catch(error => {
                    log('GET erreur: ' + error);
                });
        }

        function testPost() {
            log('Test POST...');
            fetch('visitor_counter.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=count'
            })
            .then(response => response.json())
            .then(data => {
                log('POST résultat: ' + JSON.stringify(data));
                testGet(); // Recharger le compteur
            })
            .catch(error => {
                log('POST erreur: ' + error);
            });
        }

        function testSendBeacon() {
            log('Test SendBeacon...');
            const formData = new FormData();
            formData.append('action', 'count');
            
            if (navigator.sendBeacon) {
                const success = navigator.sendBeacon('visitor_counter.php', formData);
                log('SendBeacon résultat: ' + success);
                setTimeout(testGet, 100); // Recharger le compteur après un délai
            } else {
                log('SendBeacon non supporté');
            }
        }

        // Charger le compteur au démarrage
        testGet();
    </script>
</body>
</html>

# Compteur de Visiteurs SQLite

## Description

Ce système remplace le compteur de visiteurs basé sur localStorage par une solution SQLite plus précise qui filtre efficacement les bots.

## Fonctionnalités

### Filtrage des bots
Un visiteur n'est compté que s'il remplit **tous** ces critères :
- ✅ JavaScript activé (implicite)
- ✅ Images activées (test avec une image 1x1 pixel)
- ✅ Reste au moins 2 secondes sur le site

### Comptage précis
- Le comptage se fait uniquement quand l'utilisateur quitte la page
- Utilise les événements `pagehide` (Safari) et `beforeunload` (autres navigateurs)
- Envoi asynchrone avec `navigator.sendBeacon()` pour plus de fiabilité

## Fichiers

### `visitor_counter.php`
Script PHP qui gère :
- Création automatique de la base SQLite
- Lecture du compteur actuel (`?action=get`)
- Incrémentation du compteur (`action=count`)
- Protection contre les requêtes concurrentes (transactions)
- Validation basique des requêtes

### `index.html` (modifié)
JavaScript mis à jour pour :
- Tester le chargement d'images
- Mesurer le temps passé sur la page
- Envoyer le comptage lors de la sortie
- Afficher le compteur actuel

### `visitors.db` (créé automatiquement)
Base SQLite avec une table simple :
```sql
CREATE TABLE visitor_count (
    id INTEGER PRIMARY KEY,
    count INTEGER NOT NULL DEFAULT 0
)
```

## Installation

1. Placez `visitor_counter.php` dans le même répertoire que `index.html`
2. Assurez-vous que PHP a les permissions d'écriture dans le répertoire
3. Le fichier `visitors.db` sera créé automatiquement

## Test

Utilisez `test_counter.php` pour vérifier le fonctionnement :
```bash
php test_counter.php
```

## Sécurité

- Validation basique du referer
- Protection contre les requêtes concurrentes
- Pas de données sensibles stockées
- Gestion d'erreurs appropriée

## Compatibilité

- PHP 7.0+ avec extension SQLite3/PDO
- Navigateurs modernes avec support de `fetch()` et `sendBeacon()`
- Fallback pour navigateurs plus anciens

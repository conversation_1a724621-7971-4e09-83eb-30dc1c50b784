<!doctype html>
<html lang="fr">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>J4</title>
        <link
            rel="icon"
            id="favicon"
            type="image/svg+xml"
            href="https://j4.gl/favicon-light.svg"
        />
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family:
                    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
                    sans-serif;
                background-color: #fafafa;
                color: #333;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                transition:
                    background-color 0.3s ease,
                    color 0.3s ease;
            }

            /* Dark mode styles */
            @media (prefers-color-scheme: dark) {
                body:not(.light-mode) {
                    background-color: #0f0f0f;
                    color: #e0e0e0;
                }

                body:not(.light-mode) h1 {
                    color: #f0f0f0;
                }

                body:not(.light-mode) .link-item {
                    color: #c0c0c0;
                }

                body:not(.light-mode) .link-icon {
                    fill: #e0e0e0;
                }
            }

            /* Force light mode */
            body.light-mode {
                background-color: #fafafa !important;
                color: #333 !important;
            }

            body.light-mode h1 {
                color: #2c2c2c !important;
            }

            body.light-mode .link-item {
                color: #555 !important;
            }

            body.light-mode .link-icon {
                fill: #333 !important;
            }

            /* Force dark mode */
            body.dark-mode {
                background-color: #0f0f0f !important;
                color: #e0e0e0 !important;
            }

            body.dark-mode h1 {
                color: #f0f0f0 !important;
            }

            body.dark-mode .link-item {
                color: #c0c0c0 !important;
            }

            body.dark-mode .link-icon {
                fill: #e0e0e0 !important;
            }

            /* Theme toggle button */
            .theme-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                background: none;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .theme-toggle:hover {
                opacity: 0.7;
            }

            .theme-toggle svg {
                width: 20px;
                height: 20px;
                fill: #333;
                transition: fill 0.3s ease;
            }

            body.dark-mode .theme-toggle {
                border-color: #555;
            }

            body.dark-mode .theme-toggle svg {
                fill: #e0e0e0;
            }

            /* Compteur de visiteurs */
            .visitor-counter {
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 0.7rem;
                color: #999;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .visitor-counter:hover {
                color: #666;
            }

            body.dark-mode .visitor-counter {
                color: #666;
            }

            body.dark-mode .visitor-counter:hover {
                color: #999;
            }

            .tooltip {
                position: absolute;
                bottom: 100%;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 0.75rem;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                margin-bottom: 8px;
            }

            .visitor-counter:hover .tooltip {
                opacity: 1;
                visibility: visible;
            }

            body.dark-mode .tooltip {
                background: rgba(255, 255, 255, 0.9);
                color: #333;
            }

            .container {
                text-align: center;
            }

            h1 {
                color: #2c2c2c;
                font-size: 1.8rem;
                font-weight: 400;
                margin-bottom: 60px;
                letter-spacing: 1px;
            }

            .links {
                display: flex;
                gap: 60px;
                align-items: center;
                justify-content: center;
            }

            .link-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-decoration: none;
                color: #555;
                transition: all 0.3s ease;
            }

            .link-item:hover {
                opacity: 0.6;
                transform: translateY(-3px) scale(1.5);
            }

            .link-icon {
                width: 32px;
                height: 32px;
                fill: #333;
                margin-bottom: 12px;
                transition: all 0.3s ease;
            }

            .link-text {
                font-size: 0.9rem;
                font-weight: 400;
                letter-spacing: 0.5px;
            }

            @media (max-width: 600px) {
                .links {
                    gap: 40px;
                }

                .link-icon {
                    width: 28px;
                    height: 28px;
                }

                .link-text {
                    font-size: 0.8rem;
                }

                h1 {
                    font-size: 1.5rem;
                    margin-bottom: 40px;
                }

                .theme-toggle {
                    top: 15px;
                    right: 15px;
                    padding: 6px;
                }

                .theme-toggle svg {
                    width: 18px;
                    height: 18px;
                }

                .visitor-counter {
                    bottom: 15px;
                    font-size: 0.6rem;
                }
            }

            @media (max-width: 400px) {
                .links {
                    flex-direction: column;
                    gap: 30px;
                }

                .container {
                    padding: 0 20px;
                }
            }
        </style>
    </head>
    <body>
        <button
            class="theme-toggle"
            id="themeToggle"
            aria-label="Changer le thème"
        >
            <svg id="sunIcon" viewBox="0 0 24 24">
                <path
                    d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"
                />
            </svg>
            <svg id="moonIcon" viewBox="0 0 24 24" style="display: none">
                <path
                    d="M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"
                />
            </svg>
        </button>

        <div class="container">
            <h1>J4</h1>

            <div class="links">
                <a
                    href="https://github.com/J4GL"
                    class="link-item"
                    target="_blank"
                    rel="noopener"
                >
                    <svg class="link-icon" viewBox="0 0 24 24">
                        <path
                            d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
                        />
                    </svg>
                    <span class="link-text">GitHub</span>
                </a>

                <a
                    href="https://stackoverflow.com/users/23094223/j4gl"
                    class="link-item"
                    target="_blank"
                    rel="noopener"
                >
                    <svg class="link-icon" viewBox="0 0 24 24">
                        <path
                            d="M15.725 0l-1.72 1.277-6.39 8.588 1.716 1.277 6.39-8.588L15.725 0zM8.76 11.469l8.795-3.162.584 1.62-8.795 3.162-.584-1.62zm1.54-3.746l8.13-5.27.91 1.405-8.13 5.27-.91-1.405zM9.96 3.565l6.697-6.697 1.387 1.388-6.697 6.697-1.387-1.388zM20.404 17.442v2.138H2.596v-2.138h17.808zm0-3.198H7.025v2.138H20.404v-2.138zm0-2.138H4.16v2.138H20.404v-2.138z"
                        />
                    </svg>
                    <span class="link-text">Stack Overflow</span>
                </a>

                <a
                    href="https://www.youtube.com/@J4-GL"
                    class="link-item"
                    target="_blank"
                    rel="noopener"
                >
                    <svg class="link-icon" viewBox="0 0 24 24">
                        <path
                            d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
                        />
                    </svg>
                    <span class="link-text">YouTube</span>
                </a>
            </div>
        </div>

        <div class="visitor-counter" id="visitorCounter">
            <span id="visitorCount">0</span> visiteurs
            <div class="tooltip">La plupart sont des bots (^-^)'</div>
        </div>

        <script>
            // Gestion du thème
            const themeToggle = document.getElementById("themeToggle");
            const sunIcon = document.getElementById("sunIcon");
            const moonIcon = document.getElementById("moonIcon");
            const body = document.body;

            // Vérifie les préférences système
            const systemPrefersDark = window.matchMedia(
                "(prefers-color-scheme: dark)",
            ).matches;

            // Gestion du favicon
            function updateFavicon(isDark) {
                console.log("Updating favicon");
                const favicon = document.getElementById("favicon");
                if (favicon) {
                    if (isDark) {
                        // Favicon sombre (fond noir, texte blanc)
                        favicon.href = "./dark.png";
                        console.log("dark");
                    } else {
                        // Favicon clair (fond blanc, texte noir)
                        favicon.href = "./light.png";
                        console.log("light");
                    }
                }
            }

            function updateTheme() {
                const savedTheme = localStorage.getItem("theme");
                const currentTheme =
                    savedTheme || (systemPrefersDark ? "dark" : "light");
                const isDark = currentTheme === "dark";

                if (isDark) {
                    body.classList.remove("light-mode");
                    body.classList.add("dark-mode");
                    sunIcon.style.display = "none";
                    moonIcon.style.display = "block";
                } else {
                    body.classList.remove("dark-mode");
                    body.classList.add("light-mode");
                    sunIcon.style.display = "block";
                    moonIcon.style.display = "none";
                }

                updateFavicon(isDark);
            }

            // Initialise le thème
            updateTheme();

            // Gestion du clic sur le bouton
            themeToggle.addEventListener("click", () => {
                const isDark = body.classList.contains("dark-mode");
                const newTheme = isDark ? "light" : "dark";

                localStorage.setItem("theme", newTheme);
                updateTheme(); // Réapplique le thème immédiatement
            });

            // Écoute les changements des préférences système
            window
                .matchMedia("(prefers-color-scheme: dark)")
                .addEventListener("change", (e) => {
                    if (!localStorage.getItem("theme")) {
                        updateTheme();
                    }
                });

            // Compteur de visiteurs - filtre les bots
            let pageLoadTime = Date.now();
            let hasImagesLoaded = false;
            let visitorCountSent = false;

            // Vérifier si les images sont activées (filtre les bots)
            function checkImageLoading() {
                return new Promise((resolve) => {
                    const testImg = new Image();
                    testImg.onload = () => {
                        hasImagesLoaded = true;
                        resolve(true);
                    };
                    testImg.onerror = () => resolve(false);
                    // Image 1x1 pixel transparente
                    testImg.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
                });
            }

            // Envoyer le comptage de visiteur
            function sendVisitorCount() {
                if (visitorCountSent) return;

                const timeSpent = Date.now() - pageLoadTime;

                // Critères pour un visiteur valide (pas un bot):
                // - JavaScript activé (implicite)
                // - Images activées
                // - Au moins 2 secondes sur le site
                if (timeSpent >= 2000 && hasImagesLoaded) {
                    visitorCountSent = true;

                    // Envoi asynchrone avec sendBeacon (plus fiable)
                    if (navigator.sendBeacon) {
                        navigator.sendBeacon('visitor_counter.php', 'action=count');
                    } else {
                        // Fallback pour navigateurs anciens
                        const xhr = new XMLHttpRequest();
                        xhr.open('POST', 'visitor_counter.php', false);
                        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                        xhr.send('action=count');
                    }
                }
            }

            // Charger le nombre actuel de visiteurs
            function loadVisitorCount() {
                fetch('visitor_counter.php?action=get')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById("visitorCount").textContent = data.count;
                        }
                    })
                    .catch(error => {
                        console.log('Erreur compteur:', error);
                        document.getElementById("visitorCount").textContent = '---';
                    });
            }

            // Initialiser le compteur
            async function initVisitorCounter() {
                // Tester le chargement d'images
                await checkImageLoading();

                // Afficher le compteur actuel
                loadVisitorCount();

                // Écouter la sortie de page pour compter la visite
                window.addEventListener('pagehide', sendVisitorCount);
                window.addEventListener('beforeunload', sendVisitorCount);
            }

            // Démarrer après le chargement de la page
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initVisitorCounter);
            } else {
                initVisitorCounter();
            }
        </script>
    </body>
</html>

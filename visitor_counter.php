<?php
/**
 * Compteur de visiteurs simple avec SQLite
 * Filtre les bots en vérifiant que JavaScript est activé,
 * que les images sont chargées et que l'utilisateur reste au moins 2 secondes
 */

// Configuration
define('DB_FILE', 'visitors.db');

// Headers pour CORS et sécurité
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Initialise la base de données SQLite
 */
function initDatabase() {
    try {
        $pdo = new PDO('sqlite:' . DB_FILE);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Créer la table si elle n'existe pas - juste un compteur simple
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS visitor_count (
                id INTEGER PRIMARY KEY,
                count INTEGER NOT NULL DEFAULT 0
            )
        ");

        // Insérer une ligne initiale si la table est vide
        $stmt = $pdo->query("SELECT COUNT(*) FROM visitor_count");
        if ($stmt->fetchColumn() == 0) {
            $pdo->exec("INSERT INTO visitor_count (id, count) VALUES (1, 0)");
        }

        return $pdo;
    } catch (PDOException $e) {
        error_log("Erreur base de données: " . $e->getMessage());
        return false;
    }
}

/**
 * Obtient le nombre actuel de visiteurs
 */
function getVisitorCount() {
    $pdo = initDatabase();
    if (!$pdo) {
        return false;
    }

    try {
        $stmt = $pdo->query("SELECT count FROM visitor_count WHERE id = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? (int)$result['count'] : 0;
    } catch (PDOException $e) {
        error_log("Erreur lecture compteur: " . $e->getMessage());
        return false;
    }
}

/**
 * Incrémente le compteur de visiteurs avec verrou
 */
function incrementVisitorCount() {
    $pdo = initDatabase();
    if (!$pdo) {
        return false;
    }

    try {
        // Commencer une transaction pour éviter les conditions de course
        $pdo->beginTransaction();

        // Incrémenter directement le compteur
        $stmt = $pdo->prepare("UPDATE visitor_count SET count = count + 1 WHERE id = 1");
        $stmt->execute();

        // Récupérer la nouvelle valeur
        $stmt = $pdo->query("SELECT count FROM visitor_count WHERE id = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $newCount = $result ? (int)$result['count'] : 0;

        // Valider la transaction
        $pdo->commit();

        return $newCount;
    } catch (PDOException $e) {
        // Annuler la transaction en cas d'erreur
        $pdo->rollback();
        error_log("Erreur incrémentation compteur: " . $e->getMessage());
        return false;
    }
}

/**
 * Valide la requête pour s'assurer qu'elle vient d'un visiteur légitime
 */
function validateRequest() {
    // Vérification basique du referer
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    $host = $_SERVER['HTTP_HOST'] ?? '';

    // Accepter les requêtes locales et du même domaine
    if (empty($referer) ||
        strpos($referer, $host) !== false ||
        strpos($referer, 'localhost') !== false ||
        strpos($referer, '127.0.0.1') !== false) {
        return true;
    }

    return false;
}

// Traitement principal
try {
    // Récupérer l'action depuis GET, POST ou le body de la requête
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    // Si pas d'action trouvée, vérifier le body de la requête (pour sendBeacon)
    if (empty($action)) {
        $input = file_get_contents('php://input');
        if (!empty($input)) {
            parse_str($input, $data);
            $action = $data['action'] ?? '';
        }
    }

    switch ($action) {
        case 'get':
            // Récupérer le nombre de visiteurs
            $count = getVisitorCount();
            if ($count !== false) {
                echo json_encode([
                    'success' => true,
                    'count' => $count
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Erreur lors de la lecture du compteur'
                ]);
            }
            break;
            
        case 'count':
            // Valider la requête
            if (!validateRequest()) {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => 'Requête non autorisée'
                ]);
                break;
            }

            // Incrémenter le compteur
            $newCount = incrementVisitorCount();
            if ($newCount !== false) {
                echo json_encode([
                    'success' => true,
                    'count' => $newCount
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Erreur lors de la mise à jour du compteur'
                ]);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Action non reconnue'
            ]);
            break;
    }
} catch (Exception $e) {
    error_log("Erreur générale visitor_counter.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Erreur interne du serveur'
    ]);
}
?>
